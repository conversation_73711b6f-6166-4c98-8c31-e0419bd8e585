import { createSlice } from '@reduxjs/toolkit';

// Initial state for the buyer dashboard
const initialState = {
  // Sidebar state
  activeTab: 'dashboard', // Default active tab
  isSidebarOpen: false,

  // User profile data (mock data for development)
  profile: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    profileImage: null,
  },

  // Dashboard data
  myCards: [
    { id: '1', lastFourDigits: '1234', cardType: 'mastercard' },
    { id: '2', lastFourDigits: '5678', cardType: 'mastercard' },
    { id: '3', lastFourDigits: '9012', cardType: 'mastercard' },
  ],
  myBids: [
    {
      id: '1',
      title: 'Advanced Basketball Strategies',
      coach: '<PERSON>',
      bidAmount: 75.00,
      date: '2024-01-15',
      status: 'active',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '2',
      title: 'Football Defense Techniques',
      coach: '<PERSON>',
      bidAmount: 60.00,
      date: '2024-01-10',
      status: 'won',
      image: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '3',
      title: 'Soccer Goal Keeping Masterclass',
      coach: '<PERSON>',
      bidAmount: 85.00,
      date: '2024-01-05',
      status: 'lost',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '4',
      title: 'Tennis Serve Mastery',
      coach: 'Serena Thompson',
      bidAmount: 90.00,
      date: '2024-01-20',
      status: 'active',
      image: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?q=80&w=300&h=200&auto=format&fit=crop'
    },
    {
      id: '5',
      title: 'Swimming Stroke Techniques',
      coach: 'Mark Waters',
      bidAmount: 70.00,
      date: '2024-01-18',
      status: 'won',
      image: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?q=80&w=300&h=200&auto=format&fit=crop'
    }
  ],
  myDownloads: [
    {
      id: '1',
      title: 'Basketball Offense Strategies',
      coach: 'John Smith',
      downloadDate: '2024-01-20',
      fileSize: '15.2 MB',
      fileType: 'PDF',
      image: 'https://images.unsplash.com/photo-1546519638-68e109498ffc?q=80&w=300&h=200&auto=format&fit=crop',
      price: 25.00
    },
    {
      id: '2',
      title: 'Football Training Drills',
      coach: 'Mike Johnson',
      downloadDate: '2024-01-18',
      fileSize: '25.6 MB',
      fileType: 'Video',
      image: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?q=80&w=300&h=200&auto=format&fit=crop',
      price: 35.00
    },
    {
      id: '3',
      title: 'Tennis Serve Techniques',
      coach: 'Sarah Williams',
      downloadDate: '2024-01-15',
      fileSize: '10.8 MB',
      fileType: 'PDF',
      image: 'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?q=80&w=300&h=200&auto=format&fit=crop',
      price: 20.00
    },
    {
      id: '4',
      title: 'Soccer Tactics Masterclass',
      coach: 'Carlos Rodriguez',
      downloadDate: '2024-01-12',
      fileSize: '18.4 MB',
      fileType: 'Video',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?q=80&w=300&h=200&auto=format&fit=crop',
      price: 30.00
    },
    {
      id: '5',
      title: 'Swimming Endurance Training',
      coach: 'Mark Waters',
      downloadDate: '2024-01-10',
      fileSize: '12.3 MB',
      fileType: 'PDF',
      image: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?q=80&w=300&h=200&auto=format&fit=crop',
      price: 22.00
    }
  ],
  myRequests: [
    {
      id: '1',
      title: 'Advanced Swimming Techniques',
      description: 'Looking for professional swimming techniques for competitive swimmers',
      date: '2024-01-22',
      status: 'pending',
      image: 'https://images.unsplash.com/photo-1530549387789-4c1017266635?q=80&w=300&h=200&auto=format&fit=crop',
      requestedAmount: 45.00
    },
    {
      id: '2',
      title: 'Golf Swing Analysis',
      description: 'Need a detailed analysis of golf swing mechanics',
      date: '2024-01-19',
      status: 'approved',
      image: 'https://images.unsplash.com/photo-1535131749006-b7f58c99034b?q=80&w=300&h=200&auto=format&fit=crop',
      requestedAmount: 55.00
    },
    {
      id: '3',
      title: 'Marathon Training Plan',
      description: 'Request for a 16-week marathon training plan for beginners',
      date: '2024-01-17',
      status: 'completed',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=300&h=200&auto=format&fit=crop',
      requestedAmount: 40.00
    },
    {
      id: '4',
      title: 'Boxing Fundamentals',
      description: 'Looking for basic boxing techniques and training routines',
      date: '2024-01-15',
      status: 'pending',
      image: 'https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?q=80&w=300&h=200&auto=format&fit=crop',
      requestedAmount: 35.00
    },
    {
      id: '5',
      title: 'Yoga for Athletes',
      description: 'Specialized yoga routines for sports performance enhancement',
      date: '2024-01-12',
      status: 'approved',
      image: 'https://images.unsplash.com/photo-1506629905607-d9c297d3d04b?q=80&w=300&h=200&auto=format&fit=crop',
      requestedAmount: 30.00
    }
  ],

  // Card UI state
  cardUI: {
    viewMode: 'list', // 'list' or 'add'
  },

  // Card form state
  cardForm: {
    nameOnCard: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
  },
};

const buyerDashboardSlice = createSlice({
  name: 'buyerDashboard',
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    openSidebar: (state) => {
      state.isSidebarOpen = true;
    },
    closeSidebar: (state) => {
      state.isSidebarOpen = false;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
    updateProfileImage: (state, action) => {
      state.profile.profileImage = action.payload;
    },

    // Dashboard data actions
    setMyCards: (state, action) => {
      state.myCards = action.payload;
    },
    addCard: (state, action) => {
      state.myCards.push(action.payload);
    },
    removeCard: (state, action) => {
      state.myCards = state.myCards.filter(card => card.id !== action.payload);
    },
    setMyBids: (state, action) => {
      state.myBids = action.payload;
    },
    setMyDownloads: (state, action) => {
      state.myDownloads = action.payload;
    },
    setMyRequests: (state, action) => {
      state.myRequests = action.payload;
    },

    // Card UI actions
    setCardViewMode: (state, action) => {
      state.cardUI.viewMode = action.payload;
    },

    // Card form actions
    updateCardForm: (state, action) => {
      state.cardForm = { ...state.cardForm, ...action.payload };
    },
    resetCardForm: (state) => {
      state.cardForm = initialState.cardForm;
    },
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  openSidebar,
  closeSidebar,
  updateProfile,
  updateProfileImage,
  setMyCards,
  addCard,
  removeCard,
  setMyBids,
  setMyDownloads,
  setMyRequests,
  setCardViewMode,
  updateCardForm,
  resetCardForm,
} = buyerDashboardSlice.actions;

// Export selectors
export const selectActiveTab = (state) => state.buyerDashboard.activeTab;
export const selectIsSidebarOpen = (state) => state.buyerDashboard.isSidebarOpen;
export const selectProfile = (state) => state.buyerDashboard.profile;
export const selectMyCards = (state) => state.buyerDashboard.myCards;
export const selectMyBids = (state) => state.buyerDashboard.myBids;
export const selectMyDownloads = (state) => state.buyerDashboard.myDownloads;
export const selectMyRequests = (state) => state.buyerDashboard.myRequests;
export const selectCardViewMode = (state) => state.buyerDashboard.cardUI.viewMode;
export const selectCardForm = (state) => state.buyerDashboard.cardForm;

// Export reducer
export default buyerDashboardSlice.reducer;
